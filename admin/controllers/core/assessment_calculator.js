"use strict";

define(['core/discount_calculator',
        'policy/textbook_supplies',
        'policy/pay_a_cash',
        'policy/pay_b_semestral',
        'policy/pay_c_quarterly',
        'policy/pay_d_monthly',
        'policy/discounts',
], function (DiscountsCalculator, TextbookSupplies, PayACash, PayBSemestral, PayCQuarterly, PayDMonthly, Discounts) {
    var module = {};
	const PAYMENT_PLANS = [
            {id:'PLANA',name:'[A] Cash',description:'Cash Basis'},
            {id:'PLANB',name:'[B] Semestral',description:'Semestral Plan'},
            {id:'PLANC',name:'[C] Quarterly',description:'Quarterly Plan'},
            {id:'PLAND',name:'[D] Monthly',description:'Easy/Monthly Plan'},
         ];

    function applyDiscounts(policyData, planId, Assessment) {
        return DiscountsCalculator.applyDiscounts(policyData, planId, Assessment);
    }


    function calculateAssessmentTotal(discountedTuition, policyData, textbookSuppliesTotal, Assessment) {
        let assessmentTotal = discountedTuition + policyData.miscellaneous_fees + policyData.other_fees + textbookSuppliesTotal;

        // Apply reservation fee deduction to assessment total if applicable
        if (Assessment.has_reservation_fee === 'Y') {
            assessmentTotal -= Assessment.reservation_fee_amount;
        }

        return assessmentTotal;
    }

    function updateAssessmentDetails(yearLevelKey, planId, Assessment, SMC, filter) {
        let policyData;
        switch (planId) {
            case 'PLANA':
                policyData = PayACash.PLAN_A_CASH_BASIS[yearLevelKey];
                break;
            case 'PLANB':
                policyData = PayBSemestral.PLAN_B_SEMESTRAL[yearLevelKey];
                break;
            case 'PLANC':
                policyData = PayCQuarterly.PLAN_C_QUARTERLY[yearLevelKey];
                break;
            case 'PLAND':
                policyData = PayDMonthly.PLAN_D_MONTHLY[yearLevelKey];
                break;
        }
        if (policyData) {
            // Apply discounts (including early bird)
            // The discount_calculator.js will handle setting the early_bird_message
            const discounts = applyDiscounts(policyData, planId, Assessment);

            // Calculate discount amount to apply to tuition fee
            // For cash payment plan (PLANA), apply all discounts (uponEnrol + lastDue) to the tuition fee
            // For non-cash plans, only apply the early bird discount (uponEnrol) to the tuition fee initially
            // (lastDue discounts will be applied to the payment schedules)
            let discountAmount;
            if (planId === 'PLANA') {
                // For cash plans, apply both uponEnrol and lastDue discounts to tuition
                discountAmount = discounts.uponEnrol + discounts.lastDue;
            } else {
                // For non-cash plans, only apply uponEnrol discount to tuition
                discountAmount = discounts.uponEnrol;
            }

            const discountedTuition = policyData.tuition_fee - discountAmount;

            // Get textbook and supplies data for the current year level
            let textbookSuppliesData = TextbookSupplies.TEXTBOOK_SUPPLIES[yearLevelKey];
            let textbookAmount = 0;
            let suppliesAmount = 0;
            let textbookSuppliesTotal = 0;

            // Get the default textbook and supplies amounts regardless of purchase option
            // This will be used to initialize the custom amounts and for display
            let defaultTextbookAmount = 0;
            let defaultSuppliesAmount = 0;
            let defaultTotal = 0;

            if (textbookSuppliesData) {
                // For senior high, use the appropriate track
                if (yearLevelKey === 'GY' || yearLevelKey === 'GZ') {
                    // Default to STEM if track is not specified
                    const track = Assessment.track || 'STEM';
                    defaultTextbookAmount = textbookSuppliesData['textbook_' + track] || 0;
                    defaultTotal = textbookSuppliesData['total_' + track] || 0;
                } else {
                    defaultTextbookAmount = textbookSuppliesData.textbook || 0;
                    defaultSuppliesAmount = textbookSuppliesData.school_supplies || 0;
                    defaultTotal = textbookSuppliesData.total || 0;
                }
            }

            // Store the default amounts in the summary details for display
            Assessment.summary_details = Assessment.summary_details || {};
            Assessment.summary_details.textbook_amount = defaultTextbookAmount;
            Assessment.summary_details.supplies_amount = defaultSuppliesAmount;

            // Initialize custom amounts with default values if they haven't been set yet

            // Only apply textbook and supplies fees if the user has chosen to purchase them
            if (Assessment.purchase_textbooks_supplies === 'Y') {
                if (Assessment.custom_textbook_amount === 0) {
                    Assessment.custom_textbook_amount = defaultTextbookAmount;
                }
                if (Assessment.custom_supplies_amount === 0) {
                    Assessment.custom_supplies_amount = defaultSuppliesAmount;
                }

                if (Assessment.textbooks_supplies_option === 'COMPLETE') {
                    // Use the default amounts from textbook_supplies.js
                    textbookAmount = defaultTextbookAmount;
                    suppliesAmount = defaultSuppliesAmount;
                    textbookSuppliesTotal = defaultTotal;
                } else if (Assessment.textbooks_supplies_option === 'SELECTED') {
                    // Use the custom amounts entered by the user
                    textbookAmount = Assessment.custom_textbook_amount || 0;
                    suppliesAmount = Assessment.custom_supplies_amount || 0;
                    textbookSuppliesTotal = textbookAmount + suppliesAmount;
                }
            } else {
                // If not purchasing textbooks and supplies, set all amounts to zero
                textbookAmount = 0;
                suppliesAmount = 0;
                textbookSuppliesTotal = 0;
                Assessment.custom_textbook_amount = 0;
                Assessment.custom_supplies_amount = 0;
            }

            let TuitionData = generateTuitionData(policyData, discountedTuition, discounts, textbookAmount, suppliesAmount, textbookSuppliesTotal, Assessment, filter, planId);
            let PaySchedData = generatePaySchedData(policyData, discounts, Assessment, textbookSuppliesTotal, filter, planId);

            Assessment.gross_tuition_fee = policyData.gross_tuition_fee;
            Assessment.assessment_total = calculateAssessmentTotal(discountedTuition, policyData, textbookSuppliesTotal, Assessment);
            Assessment.total_discount = discounts.total;

            return { TuitionData: TuitionData, PaySchedData: PaySchedData };
        }
    }

    function checkAllowedPlans(yearLevelKey) {
        let allowCash = !!PayACash.PLAN_A_CASH_BASIS[yearLevelKey].gross_tuition_fee;
        let allowSemester = !!PayBSemestral.PLAN_B_SEMESTRAL[yearLevelKey].gross_tuition_fee;
        let allowQuarterly = !!PayCQuarterly.PLAN_C_QUARTERLY[yearLevelKey].gross_tuition_fee;
        let allowMonthly = !!PayDMonthly.PLAN_D_MONTHLY[yearLevelKey].gross_tuition_fee;
        let allowedPaymentPlans = [];
        if (allowCash)
            allowedPaymentPlans.push(PAYMENT_PLANS[0]);
        if (allowSemester)
            allowedPaymentPlans.push(PAYMENT_PLANS[1]);
        if (allowQuarterly)
            allowedPaymentPlans.push(PAYMENT_PLANS[2]);
        if (allowMonthly)
            allowedPaymentPlans.push(PAYMENT_PLANS[3]);
        return allowedPaymentPlans;
    }

    function summarizeDetails(Assessment, PayschedData) {
        // Prepare summary data for the modal
        let yearLevelKey = Assessment.year_level_id;
        let planId = Assessment.payment_plan_id;

        // Get policy data based on payment plan
        let policyData;
        switch (planId) {
            case 'PLANA':
                policyData = PayACash.PLAN_A_CASH_BASIS[yearLevelKey];
                break;
            case 'PLANB':
                policyData = PayBSemestral.PLAN_B_SEMESTRAL[yearLevelKey];
                break;
            case 'PLANC':
                policyData = PayCQuarterly.PLAN_C_QUARTERLY[yearLevelKey];
                break;
            case 'PLAND':
                policyData = PayDMonthly.PLAN_D_MONTHLY[yearLevelKey];
                break;
        }

        // Create summary_details object to store all fee information
        Assessment.summary_details = {};

        // Store fee details
        Assessment.summary_details.tuition_fee = policyData.tuition_fee;
        Assessment.summary_details.miscellaneous_fees = policyData.miscellaneous_fees;
        Assessment.summary_details.other_fees = policyData.other_fees;
        Assessment.summary_details.total_tuition_misc_other = policyData.tuition_fee + policyData.miscellaneous_fees + policyData.other_fees;

        // Store reservation fee details
        Assessment.summary_details.has_reservation_fee = Assessment.has_reservation_fee;
        Assessment.summary_details.reservation_fee_amount = Assessment.has_reservation_fee === 'Y' ? Assessment.reservation_fee_amount : 0;

        // Store discount details
        Assessment.summary_details.total_discount = Assessment.total_discount || 0;
        Assessment.summary_details.discount_details = Assessment.discount_details || [];

        // Include early bird discount information
        Assessment.summary_details.early_bird_available = Assessment.early_bird_available;
        Assessment.summary_details.early_bird_deadline = Assessment.early_bird_deadline;
        Assessment.summary_details.early_bird_message = Assessment.early_bird_message;

        // Get textbook and supplies data for the current year level
        let textbookSuppliesData = TextbookSupplies.TEXTBOOK_SUPPLIES[yearLevelKey];
        let textbookAmount = 0;
        let suppliesAmount = 0;
        let textbookSuppliesTotal = 0;
        let isPurchasTextbooksSupplies =  Assessment.purchase_textbooks_supplies === 'Y';
        if (textbookSuppliesData && isPurchasTextbooksSupplies) {
            // For senior high, use the appropriate track
            if (yearLevelKey === 'GY' || yearLevelKey === 'GZ') {
                // Default to STEM if track is not specified
                const track = Assessment.track || 'STEM';
                textbookAmount = textbookSuppliesData['textbook_' + track] || 0;
                textbookSuppliesTotal = textbookSuppliesData['total_' + track] || 0;
            } else {
                textbookAmount = textbookSuppliesData.textbook || 0;
                suppliesAmount = textbookSuppliesData.school_supplies || 0;
                textbookSuppliesTotal = textbookSuppliesData.total || 0;
            }
        }

        // Store textbook and supplies details
        Assessment.summary_details.textbook_amount = textbookAmount;
        Assessment.summary_details.supplies_amount = suppliesAmount;
        Assessment.summary_details.textbooks_supplies_total = textbookSuppliesTotal;

        // Store payment schedule details
        Assessment.summary_details.payment_schedules = PayschedData || [];

        // Store total assessment amount
        Assessment.summary_details.assessment_total = Assessment.assessment_total;

    }

    function generateTuitionData(policyData, discountedTuition, discounts, textbookAmount, suppliesAmount, textbookSuppliesTotal, Assessment, filter, planId) {
        // Apply reservation fee deduction if applicable
        let reservationFeeDeduction = 0;
        if (Assessment.has_reservation_fee === 'Y') {
            reservationFeeDeduction = Assessment.reservation_fee_amount;
        }

        // Calculate total due including textbooks and supplies
        let totalDue = discountedTuition + policyData.miscellaneous_fees + policyData.other_fees + textbookSuppliesTotal - reservationFeeDeduction;

        // Define tuition data structure
        var tuitionItems = [
            { fee: 'Tuition Fee', amount: policyData.tuition_fee },
        ];

        // Add discount items based on payment plan
        if (planId === 'PLANA') {
            // For cash plans, show all discounts together
            const totalDiscount = discounts.uponEnrol + discounts.lastDue;
            if (totalDiscount > 0) {
                tuitionItems.push({ fee: 'Less: Total Discounts', amount: -totalDiscount, class: 'danger' });
            }
        } else {
            // For non-cash plans, only show early bird discount here
            if (discounts.uponEnrol > 0) {
                tuitionItems.push({ fee: 'Less: Initial Discounts', amount: -discounts.uponEnrol, class: 'danger' });
            }
        }

        tuitionItems.push({ fee: 'Net Tuition Fee', amount: discountedTuition });
        tuitionItems.push({ fee: 'Msc Fees', amount: policyData.miscellaneous_fees });
        tuitionItems.push({ fee: 'Other Fees', amount: policyData.other_fees });

        // Add textbook and supplies fees
        if (textbookAmount > 0) {
            tuitionItems.push({ fee: 'Textbooks', amount: textbookAmount });
        }
        if (suppliesAmount > 0) {
            tuitionItems.push({ fee: 'School Supplies', amount: suppliesAmount });
        }

        // Add reservation fee deduction if applicable
        if (reservationFeeDeduction > 0) {
            tuitionItems.push({ fee: 'Less: Reservation Fee', amount: -reservationFeeDeduction, class: 'danger' });
        }

        tuitionItems.push({ fee: 'Upon Enrollment', amount: totalDue, class: 'warning' });

        // Add payment schedule items for non-cash payment plans
        return tuitionItems.map(item => ({
            ...item,
            display_amount: filter('currency')(item.amount, '₱', 2)
        }));
    }

    function generatePaySchedData(policyData, discounts, Assessment, textbookSuppliesTotal, filter, planId) {
        let scheduleData = [];

        // First pass: Apply discounts to first payment and prepare data structure
        for (let index = 0; index < policyData.due_dates.length; index++) {
            let date = policyData.due_dates[index];
            let amount = policyData.due_amounts[index];

            // For cash payment plan, apply all discounts to the upon enrollment amount
            // For non-cash plans, apply early bird to first payment
            if (index === 0) {
                // Apply upon enrollment discount to first due date
                amount = amount - discounts.uponEnrol;

                // For cash payment plan (PLANA), also apply the last due discount to the first payment
                if (planId === 'PLANA' && discounts.lastDue > 0) {
                    amount = amount - discounts.lastDue;
                }

                // Apply reservation fee deduction to first payment (Upon Enrollment)
                if (Assessment.has_reservation_fee === 'Y') {
                    amount = amount - Assessment.reservation_fee_amount;
                }

                // Add textbook and supplies fees to first payment (Upon Enrollment)
                // These fees are always paid in full upon enrollment regardless of payment plan
                if (textbookSuppliesTotal > 0) {
                    amount = amount + textbookSuppliesTotal;
                }
            }

            scheduleData.push({
                schedule: date,
                amount: amount,
                original_amount: amount, // Keep track of original amount before discount distribution
                display_amount: filter('currency')(amount, '₱', 2)
            });
        }

        // Apply last due discount to payment schedules for non-cash plans
        // Distribute discounts across multiple payments, allowing payments to be reduced to zero
        if (planId !== 'PLANA' && discounts.lastDue > 0 && scheduleData.length > 1) { // Only for non-cash plans with lastDue discount and more than one payment
            let remainingDiscount = discounts.lastDue;

            // Start from the last payment and work backwards
            for (let i = scheduleData.length - 1; i >= 1 && remainingDiscount > 0; i--) {
                // Calculate how much discount can be applied to this payment
                // Allow the payment to be reduced to zero (no 1 peso minimum)
                const maxDiscountForThisPayment = scheduleData[i].amount;
                const discountToApply = Math.min(remainingDiscount, maxDiscountForThisPayment);

                // Apply the discount to this payment
                scheduleData[i].amount -= discountToApply;
                remainingDiscount -= discountToApply;

                // Round to 2 decimal places to avoid floating point issues
                scheduleData[i].amount = Math.round(scheduleData[i].amount * 100) / 100;

                // Recalculate display amount
                scheduleData[i].display_amount = filter('currency')(scheduleData[i].amount, '₱', 2);
            }

            // If there's still remaining discount that couldn't be distributed
            // Apply it to the first payment (Upon Enrollment)
            if (remainingDiscount > 0) {
                console.log('Applying remaining discount to Upon Enrollment payment. Remaining discount:', remainingDiscount);
                scheduleData[0].amount -= remainingDiscount;
                scheduleData[0].amount = Math.round(scheduleData[0].amount * 100) / 100; // Round to 2 decimal places
                scheduleData[0].display_amount = filter('currency')(scheduleData[0].amount, '₱', 2);
            }
        }

        return scheduleData;
    }

    // Scholarship-related functions
    function updateScholarshipFees(items, scope) {
        scope.ScholarshipFeeData = items;

        // Calculate the total discount from the difference between original amounts and final amounts
        let totalDiscount = 0;
        if (items && items.length > 0) {
            items.forEach(function(item) {
                totalDiscount += (item.amount - item.final_amount);
            });
        }
        
        // Update the academic scholarship discount in the assessment
        if (scope.Assessment) {
            scope.Assessment.academic_scholarship_discount = totalDiscount;
        }
    }

    function updateScholarshipSchedule(items, scope) {
        scope.ScholarshipSchedData = items;
        // Additional logic to update payment schedule
    }

    function populateScholarshipFees(Assessment, scope) {
        if (!Assessment.year_level_id || !Assessment.payment_plan_id) {
            scope.ScholarshipFeeData = [];
            return;
        }

        // Get the current tuition data to extract fee amounts
        let yearLevelKey = Assessment.year_level_id;
        let planId = Assessment.payment_plan_id;

        // Get policy data based on payment plan
        let policyData;
        switch (planId) {
            case 'PLANA':
                policyData = PayACash.PLAN_A_CASH_BASIS[yearLevelKey];
                break;
            case 'PLANB':
                policyData = PayBSemestral.PLAN_B_SEMESTRAL[yearLevelKey];
                break;
            case 'PLANC':
                policyData = PayCQuarterly.PLAN_C_QUARTERLY[yearLevelKey];
                break;
            case 'PLAND':
                policyData = PayDMonthly.PLAN_D_MONTHLY[yearLevelKey];
                break;
        }

        if (policyData) {
            // Calculate academic scholarship discount if applicable
            let tuitionDiscount = 0;
            if (Assessment.has_academic_scholarship === 'Y' && Assessment.academic_scholarship_type && Assessment.student_source) {
                const scholarshipType = Assessment.academic_scholarship_type;
                let discountType = '';

                if (scholarshipType === 'FULL') {
                    discountType = Assessment.student_source === 'PUBLIC' ? 'ACADEMIC_SCHOLARSHIP_FULL_PUBLIC' : 'ACADEMIC_SCHOLARSHIP_FULL_PRIVATE';
                } else if (scholarshipType === 'PARTIAL') {
                    discountType = Assessment.student_source === 'PUBLIC' ? 'ACADEMIC_SCHOLARSHIP_PARTIAL_PUBLIC' : 'ACADEMIC_SCHOLARSHIP_PARTIAL_PRIVATE';
                }

                // Calculate discount using the Discounts module
                if (Discounts && Discounts[discountType]) {
                    const discount = Discounts[discountType];
                    if (discount.unit === 'PERCENT') {
                        tuitionDiscount = (policyData.gross_tuition_fee * discount.amount) / 100;
                    }
                }
            }

            // Calculate final tuition amount after scholarship discount
            let finalTuitionAmount = policyData.gross_tuition_fee - tuitionDiscount;

            scope.ScholarshipFeeData = [
                {
                    fee: 'TUI',
                    amount: policyData.gross_tuition_fee,
                    final_amount: finalTuitionAmount
                },
                {
                    fee: 'MSC',
                    amount: policyData.miscellaneous_fees,
                    final_amount: policyData.miscellaneous_fees
                },
                {
                    fee: 'OTH',
                    amount: policyData.other_fees,
                    final_amount: policyData.other_fees
                }
            ];
        } else {
            scope.ScholarshipFeeData = [];
        }
    }

    module.checkAllowedPlans = checkAllowedPlans;
    module.applyDiscounts = applyDiscounts;
    module.updateAssessmentDetails = updateAssessmentDetails;
    module.summarizeDetails = summarizeDetails;
    module.updateScholarshipFees = updateScholarshipFees;
    module.updateScholarshipSchedule = updateScholarshipSchedule;
    module.populateScholarshipFees = populateScholarshipFees;
	module.__PAYMENT_PLANS =  PAYMENT_PLANS;

    return module;
});
