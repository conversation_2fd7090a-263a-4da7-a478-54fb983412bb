"use strict";

define(['policy/discounts'], function(Discounts) {
    var module = {};

    /**
     * Checks eligibility for various discount programs based on year level
     * @param {String} yearLevel - The student's year level ID
     * @returns {Object} Eligibility flags for different discount programs
     */
    module.checkDiscountEligibility = function(yearLevel) {
        return {
            // Check if the year level is G7 to G10 for ESC eligibility
            isEscEligible: ['G7', 'G8', 'G9', 'GX'].includes(yearLevel),

            // Check if the year level is G11 or G12 for Voucher eligibility
            isVoucherEligible: ['GY', 'GZ'].includes(yearLevel),

            // Check if the year level is G7 to G12 for Academic Scholarship eligibility
            isAcademicScholarshipEligible: ['G7', 'G8', 'G9', 'GX', 'GY', 'GZ'].includes(yearLevel)
        };
    };

    /**
     * Assigns automatic discounts based on current date and student information
     * @param {Object} Assessment - The assessment object
     * @param {Object} Discounts - The discounts configuration object
     * @returns {Object} Updated assessment object with auto-discounts applied
     */
    module.assignAutoDiscounts = function(Assessment, Discounts) {
        // Early Bird Discount
        let earlyBirdCutOff = new Date(Discounts.EARLY_BIRD_DISCOUNT.enroll_on_before);
        let today = new Date();

        // Apply early bird discount if today is before the cutoff date and payment plan is cash
        if (today <= earlyBirdCutOff && Assessment.payment_plan_id == 'PLANA') {
            Assessment.early_bird = 'Y';
        } else {
            Assessment.early_bird = 'N';
        }

        // ESC Discount for Senior High
        if (Assessment.year_level_id == 'GY' || Assessment.year_level_id == 'GZ') {
            // Set student source based on previous school type
            Assessment.student_source = Assessment.prev_school_type == 'PUB' ? 'PUBLIC' : 'PRIVATE';
        }

        return Assessment;
    };

    /**
     * Updates discount visibility flags based on student eligibility
     * @param {Object} $scope - The controller scope
     * @param {String} yearLevel - The student's year level ID
     */
    module.updateDiscountVisibility = function($scope, yearLevel) {
        // Get eligibility flags
        const eligibility = module.checkDiscountEligibility(yearLevel);

        // Update scope with eligibility flags
        $scope.isEscEligible = eligibility.isEscEligible;
        $scope.isVoucherEligible = eligibility.isVoucherEligible;
        $scope.isAcademicScholarshipEligible = eligibility.isAcademicScholarshipEligible;

        // Update discount panel visibility based on eligibility
        $scope.showEscVoucher = eligibility.isEscEligible || eligibility.isVoucherEligible;
        $scope.showAcademicScholarship = eligibility.isAcademicScholarshipEligible;
    };

    /**
     * Validates if total discounts exceed the gross tuition fee
     * @param {Object} Assessment - The assessment object
     * @returns {Boolean} True if discounts exceed tuition, false otherwise
     */
    module.validateDiscountCap = function(Assessment) {
        // Get total discount amount and gross tuition fee
        const totalDiscountAmount = parseFloat(Assessment.total_discount || 0);
        const grossTuitionFee = parseFloat(Assessment.gross_tuition_fee || 0);

        // Check if total discounts exceed the gross tuition fee
        if (totalDiscountAmount > grossTuitionFee && grossTuitionFee > 0) {
            // Set flags and calculate excess amount
            Assessment.discount_exceeds_tuition = true;
            Assessment.discount_excess_amount = (totalDiscountAmount - grossTuitionFee).toFixed(2);
            return true;
        } else {
            // Clear flags
            Assessment.discount_exceeds_tuition = false;
            Assessment.discount_excess_amount = 0;
            return false;
        }
    };

    /**
     * Prepares discount text descriptions from the discount policy
     * @param {Object} Discounts - The discounts configuration object
     * @returns {Object} Discount text descriptions keyed by discount type
     */
    module.prepareDiscountText = function(Discounts) {
        let discountText = {};
        Object.keys(Discounts).forEach((key) => {
            discountText[key] = Discounts[key].policy;
        });
        return discountText;
    };

    /**
     * Sets up discount-related watchers
     * @param {Object} $selfScope - The parent scope
     * @param {Object} $scope - The controller scope
     * @param {Function} updateAssessmentDetails - Function to update assessment details
     */
    module.setupDiscountWatchers = function($selfScope, $scope, updateAssessmentDetails) {
        // Watch for year level changes to determine eligibility for various programs
        $selfScope.$watch('ASC.Assessment.year_level_id', function(yearLevel) {
            module.updateDiscountVisibility($scope, yearLevel);
        });

        // Watch for academic scholarship type changes to automatically set has_academic_scholarship
        $selfScope.$watch('ASC.Assessment.academic_scholarship_type', function(scholarshipType) {
            if (!$scope.Assessment) return;
            
            // Automatically set has_academic_scholarship based on scholarship type selection
            if (scholarshipType && (scholarshipType === 'FULL' || scholarshipType === 'PARTIAL')) {
                $scope.Assessment.has_academic_scholarship = 'Y';
                // Populate scholarship fees when Academic Scholar is selected
                if ($scope.populateScholarshipFees) {
                    $scope.populateScholarshipFees();
                }
            } else {
                $scope.Assessment.has_academic_scholarship = 'N';
                $scope.Assessment.academic_scholarship_discount = 0;
                // Clear scholarship fees when Academic Scholar is not selected
                $scope.ScholarshipFeeData = [];
            }
        });

        // Watch for year level and payment plan changes to repopulate scholarship fees
        $selfScope.$watchGroup(['ASC.Assessment.year_level_id', 'ASC.Assessment.payment_plan_id'], function() {
            if (!$scope.Assessment) return;

            // Repopulate scholarship fees if Academic Scholar is selected
            if ($scope.Assessment.has_academic_scholarship === 'Y' && $scope.populateScholarshipFees) {
                $scope.populateScholarshipFees();
            }
        });

        // Watch group for all discount-related fields
        $selfScope.$watchGroup([
            'ASC.Assessment.has_reservation_fee',
            'ASC.Assessment.reservation_fee_amount',
            'ASC.Assessment.has_waived_registration_fee',
            'ASC.Assessment.early_bird',
            'ASC.Assessment.has_child_discount',
            'ASC.Assessment.sibling_order',
            'ASC.Assessment.is_employee_child',
            'ASC.Assessment.employee_years',
            'ASC.Assessment.esc_grantee',
            'ASC.Assessment.student_source',
            'ASC.Assessment.has_referral',
            'ASC.Assessment.has_sport_scholarship',
            'ASC.Assessment.has_academic_scholarship',
            'ASC.Assessment.academic_scholarship_type',
            'ASC.Assessment.academic_scholarship_discount',
            'ASC.Assessment.purchase_textbooks_supplies',
            'ASC.Assessment.textbooks_supplies_option',
            'ASC.Assessment.custom_textbook_amount',
            'ASC.Assessment.custom_supplies_amount',
            
        ], function() {
            if (!$scope.Assessment) return;

            // Only recompute if we have a year level and payment plan selected
            if ($scope.Assessment.year_level_id && $scope.Assessment.payment_plan_id) {
                updateAssessmentDetails($scope.Assessment.year_level_id, $scope.Assessment.payment_plan_id);
            }
        });
    };

    return module;
});
