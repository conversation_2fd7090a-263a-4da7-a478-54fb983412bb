"use strict";

define(['policy/discounts'], function (Discounts) {
    var module = {};


    function calculateDiscount(tuitionFee, discountType) {
        let discountAmount = 0;
        switch (discountType) {
            case 'EARLY_BIRD_DISCOUNT':
            case 'SIBLING_DISCOUNT_1ST':
            case 'SIBLING_DISCOUNT_2ND':
            case 'SIBLING_DISCOUNT_3RD':
            case 'SIBLING_DISCOUNT_4TH':
            case 'SIBLING_DISCOUNT_5TH':
            case 'EMPLOYEE_CHILD_DISCOUNT_1YR':
            case 'EMPLOYEE_CHILD_DISCOUNT_2YR':
            case 'EMPLOYEE_CHILD_DISCOUNT_3YR':
            case 'EMPLOYEE_CHILD_DISCOUNT_4YR':
            case 'REFERRAL_DISCOUNT':
            case 'SPORTS_SCHOLARSHIP':
            case 'ACADEMIC_SCHOLARSHIP_FULL_PUBLIC':
            case 'ACADEMIC_SCHOLARSHIP_FULL_PRIVATE':
            case 'ACADEMIC_SCHOLARSHIP_PARTIAL_PUBLIC':
            case 'ACADEMIC_SCHOLARSHIP_PARTIAL_PRIVATE':
                const discount = Discounts[discountType];
                if (discount && discount.unit === 'PERCENT') {
                    discountAmount = (tuitionFee * discount.amount) / 100;
                } else if (discount && discount.unit === 'PESO') {
                    discountAmount = discount.amount;
                }
                break;
            case 'ESC_GRANT':
            case 'SHS_VOUCHER_PUBLIC':
            case 'SHS_VOUCHER_PRIVATE':
                const fixedDiscount = Discounts[discountType];
                if (fixedDiscount) {
                    discountAmount = fixedDiscount.amount;
                }
                break;
            default:
                discountAmount = 0;
        }
        return discountAmount;
    }

    function applyEarlyBirdDiscount(tuitionFee, planId, Assessment, discountDetails) {
        Assessment.early_bird_discount = 0;
        if (planId !== 'PLANA') {
            Assessment.early_bird = 'N';
        }

        // Check if current date is on or before the early bird deadline
        const currentDate = new Date();
        const earlyBirdDeadline = new Date(Discounts.EARLY_BIRD_DISCOUNT.enroll_on_before);
        const isBeforeDeadline = currentDate <= earlyBirdDeadline;

        // Store the early bird deadline info in the Assessment object
        Assessment.early_bird_deadline = Discounts.EARLY_BIRD_DISCOUNT.enroll_on_before;
        Assessment.early_bird_available = isBeforeDeadline;
        Assessment.early_bird_discount_percent = Discounts.EARLY_BIRD_DISCOUNT.amount;

        // Set a default message if early bird is not available
        if (!isBeforeDeadline) {
            Assessment.early_bird_message = "Early Bird Discount is only applicable until " +
                Discounts.EARLY_BIRD_DISCOUNT.enroll_on_before + ". The discount period has ended.";
        }

        // Only apply early bird discount if it's before the deadline
        if (Assessment.early_bird === 'Y' && planId === 'PLANA' && tuitionFee > 0) {
            if (isBeforeDeadline) {
                const earlyBirdDiscount = calculateDiscount(tuitionFee, 'EARLY_BIRD_DISCOUNT');
                discountDetails.push({
                    type: 'Early Bird',
                    amount: earlyBirdDiscount,
                    percentage: Discounts.EARLY_BIRD_DISCOUNT.amount,
                    apply_on: Discounts.EARLY_BIRD_DISCOUNT.apply_on
                });
                Assessment.early_bird_discount = earlyBirdDiscount;
                return earlyBirdDiscount;
            } else {
                // Early bird was selected but deadline has passed
                Assessment.early_bird_expired = true;
                Assessment.early_bird = 'N'; // Reset to 'N' since it's not applicable

                // Set the early bird message for when it was selected but expired
                Assessment.early_bird_message = "Early Bird Discount is only applicable until " +
                    Discounts.EARLY_BIRD_DISCOUNT.enroll_on_before + ". The discount period has ended.";
            }
        }
        return 0;
    }

    function applySiblingDiscount(tuitionFee, Assessment, discountDetails) {
        Assessment.sibling_order_discount = 0;
        if (Assessment.has_child_discount === 'Y' && Assessment.sibling_order && tuitionFee > 0) {
            const siblingOrder = Assessment.sibling_order;
            const siblingDiscountType = `SIBLING_DISCOUNT_${siblingOrder}${getOrdinalSuffix(siblingOrder).toUpperCase()}`;
            const siblingDiscount = calculateDiscount(tuitionFee, siblingDiscountType);
            discountDetails.push({
                type: `Sibling (${siblingOrder}${getOrdinalSuffix(siblingOrder)})`,
                amount: siblingDiscount,
                percentage: Discounts[siblingDiscountType].amount,
                apply_on: Discounts[siblingDiscountType].apply_on
            });
            Assessment.sibling_order_discount = siblingDiscount;
            return siblingDiscount;
        }
        return 0;
    }

    function applyEmployeeChildDiscount(tuitionFee, Assessment, discountDetails) {
        Assessment.employee_child_discount = 0;
        if (Assessment.is_employee_child === 'Y' && Assessment.employee_years && tuitionFee > 0) {
            const employeeDiscountType = `EMPLOYEE_CHILD_DISCOUNT_${Assessment.employee_years}YR`;
            const employeeDiscount = calculateDiscount(tuitionFee, employeeDiscountType);
            discountDetails.push({
                type: `Employee Child (${Assessment.employee_years} Years)`,
                amount: employeeDiscount,
                percentage: Discounts[employeeDiscountType].amount,
                apply_on: Discounts[employeeDiscountType].apply_on
            });
            Assessment.employee_child_discount = employeeDiscount;
            return employeeDiscount;
        }
        return 0;
    }

    function applyEscGrant(tuitionFee, Assessment, discountDetails) {
        Assessment.esc_discount = 0;
        const yearLevel = Assessment.year_level_id;
        if (['G7', 'G8', 'G9', 'GX'].includes(yearLevel) && Assessment.esc_grantee === 'Y' && tuitionFee > 0) {
            const escDiscount = calculateDiscount(tuitionFee, 'ESC_GRANT');
            discountDetails.push({
                type: 'ESC Grant',
                amount: escDiscount,
                fixed: Discounts.ESC_GRANT.amount,
                apply_on: Discounts.ESC_GRANT.apply_on
            });
            Assessment.esc_discount = escDiscount;
            return escDiscount;
        }
        return 0;
    }

    function applyShsVoucher(tuitionFee, Assessment, discountDetails) {
        Assessment.voucher_discount = 0;
        const yearLevel = Assessment.year_level_id;
        if (['GY', 'GZ'].includes(yearLevel) && Assessment.student_source && tuitionFee > 0) {
            const voucherType = Assessment.student_source === 'PUBLIC' ? 'SHS_VOUCHER_PUBLIC' : 'SHS_VOUCHER_PRIVATE';
            const voucherDiscount = calculateDiscount(tuitionFee, voucherType);
            discountDetails.push({
                type: `SHS Voucher (${Assessment.student_source})`,
                amount: voucherDiscount,
                fixed: Discounts[voucherType].amount,
                apply_on: Discounts[voucherType].apply_on
            });
            Assessment.voucher_discount = voucherDiscount;
            return voucherDiscount;
        }
        return 0;
    }

    function applyReferralDiscount(tuitionFee, Assessment, discountDetails) {
        Assessment.referral_discount = 0;
        if (Assessment.has_referral === 'Y' && tuitionFee > 0) {
            const referralDiscount = calculateDiscount(tuitionFee, 'REFERRAL_DISCOUNT');
            discountDetails.push({
                type: 'Referral',
                amount: referralDiscount,
                fixed: Discounts.REFERRAL_DISCOUNT.amount,
                apply_on: Discounts.REFERRAL_DISCOUNT.apply_on
            });
            Assessment.referral_discount = referralDiscount;
            return referralDiscount;
        }
        return 0;
    }

    function applyWaivedRegistrationFee(_, Assessment, discountDetails) {
        // Note: First parameter (tuitionFee) is not used in this function but kept for consistency
        Assessment.waived_registration_fee_discount = 0;
        if (Assessment.has_waived_registration_fee === 'Y') {
            const waivedRegistrationFeeDiscount = Discounts.WAIVED_REGISTRATION_FEE.amount;
            discountDetails.push({
                type: 'Waived Registration Fee',
                amount: waivedRegistrationFeeDiscount,
                fixed: waivedRegistrationFeeDiscount,
                apply_on: Discounts.WAIVED_REGISTRATION_FEE.apply_on
            });
            Assessment.waived_registration_fee_discount = waivedRegistrationFeeDiscount;
            return waivedRegistrationFeeDiscount;
        }
        return 0;
    }

    function applySportScholarshipDiscount(tuitionFee, Assessment, discountDetails) {
        Assessment.sport_scholarship_discount = 0;
        if (Assessment.has_sport_scholarship === 'Y' && tuitionFee > 0) {
            const sportScholarshipDiscount = calculateDiscount(tuitionFee, 'SPORTS_SCHOLARSHIP');
            discountDetails.push({
                type: 'Sports Scholarship',
                amount: sportScholarshipDiscount,
                fixed: Discounts.SPORTS_SCHOLARSHIP.amount,
                apply_on: 'Upon Enrollment'
            });
            return sportScholarshipDiscount;
        }
        return 0;
    }

function applyAcademicScholarshipDiscount(tuitionFee, Assessment, discountDetails) {
    if (Assessment.has_academic_scholarship === 'Y' && tuitionFee > 0) {
        const scholarshipType = Assessment.academic_scholarship_type;
        let scholarshipAmount = 0;
        let discountType = '';

        // Check if academic_scholarship_discount is already set (from scholarship fee table)
        if (Assessment.academic_scholarship_discount && Assessment.academic_scholarship_discount > 0) {
            scholarshipAmount = Assessment.academic_scholarship_discount;
        } else {
            // Calculate discount using policy if not set from scholarship table
            if (scholarshipType === 'FULL') {
                discountType = Assessment.student_source === 'PUBLIC' ? 'ACADEMIC_SCHOLARSHIP_FULL_PUBLIC' : 'ACADEMIC_SCHOLARSHIP_FULL_PRIVATE';
            } else if (scholarshipType === 'PARTIAL') {
                discountType = Assessment.student_source === 'PUBLIC' ? 'ACADEMIC_SCHOLARSHIP_PARTIAL_PUBLIC' : 'ACADEMIC_SCHOLARSHIP_PARTIAL_PRIVATE';
            }

            if (Discounts[discountType]) {
                const discount = Discounts[discountType];
                if (discount.unit === 'PERCENT') {
                    scholarshipAmount = (tuitionFee * discount.amount) / 100;
                }
                Assessment.academic_scholarship_discount = scholarshipAmount;
            }
        }

        if (scholarshipAmount > 0) {
            // Get the discount percentage for display
            let discountPercentage = 0;
            if (scholarshipType === 'FULL') {
                discountType = Assessment.student_source === 'PUBLIC' ? 'ACADEMIC_SCHOLARSHIP_FULL_PUBLIC' : 'ACADEMIC_SCHOLARSHIP_FULL_PRIVATE';
            } else if (scholarshipType === 'PARTIAL') {
                discountType = Assessment.student_source === 'PUBLIC' ? 'ACADEMIC_SCHOLARSHIP_PARTIAL_PUBLIC' : 'ACADEMIC_SCHOLARSHIP_PARTIAL_PRIVATE';
            }

            if (Discounts[discountType]) {
                discountPercentage = Discounts[discountType].amount;
            }

            discountDetails.push({
                type: `Academic Scholarship (${scholarshipType})`,
                amount: scholarshipAmount,
                percentage: discountPercentage,
                apply_on: Discounts[discountType] ? Discounts[discountType].apply_on : 'Last Due'
            });
            return scholarshipAmount;
        }
    } else {
        // Reset academic scholarship discount if not applicable
        Assessment.academic_scholarship_discount = 0;
    }
    return 0;
}

    function applyDiscounts(policyData, planId, Assessment) {
        if (!Assessment) return;
        let uponEnrolDiscount = 0;
        let lastDueDiscount = 0;
        let discountDetails = [];
        Assessment.discount_details = [];
        Assessment.gross_tuition_fee = policyData ? policyData.gross_tuition_fee : 0;
        Assessment.total_discount = 0;
        Assessment.assessment_total = 0;

        let tuitionFee = Assessment.gross_tuition_fee;

        uponEnrolDiscount += applyEarlyBirdDiscount(tuitionFee, planId, Assessment, discountDetails);
        uponEnrolDiscount += applyWaivedRegistrationFee(tuitionFee, Assessment, discountDetails);
        lastDueDiscount += applySiblingDiscount(tuitionFee, Assessment, discountDetails);
        lastDueDiscount += applyEmployeeChildDiscount(tuitionFee, Assessment, discountDetails);
        lastDueDiscount += applyEscGrant(tuitionFee, Assessment, discountDetails);
        lastDueDiscount += applyShsVoucher(tuitionFee, Assessment, discountDetails);
        lastDueDiscount += applyReferralDiscount(tuitionFee, Assessment, discountDetails);
        lastDueDiscount += applySportScholarshipDiscount(tuitionFee, Assessment, discountDetails);
        lastDueDiscount += applyAcademicScholarshipDiscount(tuitionFee, Assessment, discountDetails);

        // Calculate total discount amount
        let totalDiscountAmount = uponEnrolDiscount + lastDueDiscount;

        // Check if total discounts exceed the gross tuition fee (cash tuition fee)
        const grossTuitionFee = Assessment.gross_tuition_fee;
        Assessment.discount_exceeds_tuition = false;

        if (totalDiscountAmount > grossTuitionFee && grossTuitionFee > 0) {
            // Set a flag to indicate that discounts exceed tuition
            Assessment.discount_exceeds_tuition = true;
            Assessment.discount_excess_amount = (totalDiscountAmount - grossTuitionFee).toFixed(2);
        }

        // Store discount details for displayYearLevels
        Assessment.discount_details = discountDetails;
        Assessment.total_discount = totalDiscountAmount.toFixed(2);

        return {
            uponEnrol: uponEnrolDiscount,
            lastDue: lastDueDiscount,
            total: totalDiscountAmount.toFixed(2)
        };
    }

    // Helper function to get ordinal suffix
    function getOrdinalSuffix(n) {
        const j = n % 10;
        const k = n % 100;
        if (j == 1 && k != 11) return "st";
        if (j == 2 && k != 12) return "nd";
        if (j == 3 && k != 13) return "rd";
        return "th";
    }

    module.applyDiscounts = applyDiscounts;

    return module;
});
